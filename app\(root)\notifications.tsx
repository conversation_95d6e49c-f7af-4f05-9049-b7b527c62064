import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  RefreshControl,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import moment from "moment";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import { supabase } from "@/lib/supabase";
import { notificationsAPI } from "@/lib/notificationsApi";
import { useTheme } from "@/src/context/ThemeContext";

interface Notification {
  id: string;
  type: "like" | "comment" | "follow" | "mention"; // Added "mention" type
  sender_id: string;
  sender: {
    username: string;
    avatar_url: string;
  };
  post_id?: string;
  comment_id?: string;
  created_at: string;
  is_read: boolean;
}

export default function NotificationsScreen() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [userId, setUserId] = useState<string | null>(null);
  const router = useRouter();
  const { colors, isDarkMode } = useTheme();

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUserId(user?.id || null);
    };

    fetchUser();
  }, []);

  const fetchNotifications = async () => {
    if (!userId) return;
    try {
      console.log("Fetching notifications for user:", userId);
      const data = await notificationsAPI.getNotifications(userId);
      console.log("Fetched notifications:", data);
      setNotifications(data);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    if (userId) {
      fetchNotifications();

      const subscription = supabase
        .channel("notifications")
        .on(
          "postgres_changes",
          {
            event: "INSERT",
            schema: "public",
            table: "notifications",
            filter: `recipient_id=eq.${userId}`,
          },
          async (payload) => {
            console.log("New notification received:", payload);
            const newNotification = payload.new as any;
            const { data: senderData, error } = await supabase
              .from("profiles")
              .select("username, avatar_url")
              .eq("id", newNotification.sender_id)
              .single();

            console.log("Sender data from profiles:", senderData);
            console.log("Sender data error:", error);

            if (!error && senderData) {
              setNotifications((prev) => [
                {
                  id: newNotification.id,
                  type: newNotification.type,
                  sender_id: newNotification.sender_id,
                  sender: {
                    username: senderData.username,
                    avatar: senderData.avatar_url,
                  },
                  post_id: newNotification.post_id,
                  comment_id: newNotification.comment_id,
                  created_at: newNotification.created_at,
                  is_read: newNotification.is_read,
                },
                ...prev,
              ]);
            }
          }
        )
        .subscribe();

      return () => {
        subscription.unsubscribe();
      };
    }
  }, [userId]);

  const handleNotificationPress = async (notification: Notification) => {
    try {
      console.log("Handling notification press:", notification);
      await notificationsAPI.markAsRead(notification.id);
      console.log("Marked notification as read:", notification.id);

      if (notification.type === "follow") {
        router.push({
          pathname: "/userProfile/[id]",
          params: {
            id: notification.sender_id,
            avatar: notification.sender.avatar_url,
          },
        });
      } else if (notification.post_id) {
        router.push({
          pathname: "/post/[id]",
          params: { id: notification.post_id },
        });
      }

      setNotifications((prev) =>
        prev.map((n) =>
          n.id === notification.id ? { ...n, is_read: true } : n
        )
      );
    } catch (error) {
      console.error("Error handling notification:", error);
    }
  };

  const handleDelete = async (notificationId: string) => {
    try {
      await notificationsAPI.deleteNotification(notificationId);
      setNotifications((prev) => prev.filter((n) => n.id !== notificationId));
    } catch (error) {
      console.error("Error deleting notification:", error);
    }
  };

  const renderNotification = ({
    item: notification,
  }: {
    item: Notification;
  }) => {
    const timeAgo = moment(notification.created_at).fromNow();

    return (
      <TouchableOpacity
        style={[
          styles.notificationItem,
          { borderBottomColor: `${colors.primary}20` },
          !notification.is_read && { backgroundColor: `${colors.primary}10` },
        ]}
        onPress={() => handleNotificationPress(notification)}
      >
        <Image
          source={{ uri: notification.sender.avatar_url }}
          style={[styles.avatar, { borderColor: `${colors.primary}30` }]}
        />
        <View style={styles.notificationContent}>
          <Text className="font-rubik-bold" style={[styles.username, { color: colors.text }]}>
            {notification.sender.username}
          </Text>
          <Text className="font-rubik-medium" style={[styles.notificationText, { color: colors.textSecondary }]}>
            {notification.type === "like" && "liked your post"}
            {notification.type === "comment" && "commented on your post"}
            {notification.type === "follow" && "started following you"}
            {notification.type === "mention" && "mentioned you in a post"}
          </Text>
          <Text className="font-rubik-medium" style={[styles.timeAgo, { color: colors.textTertiary }]}>
            {timeAgo}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={(e) => {
            e.stopPropagation();
            handleDelete(notification.id);
          }}
        >
          <Ionicons name="trash-outline" size={20} color="#FF6B6B" />
        </TouchableOpacity>
        {!notification.is_read && (
          <View style={styles.unreadDot}>
            <Ionicons name="ellipse" size={10} color={colors.primary} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <LinearGradient
        colors={isDarkMode
          ? [colors.background, colors.backgroundSecondary, colors.backgroundTertiary]
          : [colors.background, colors.backgroundSecondary, colors.backgroundTertiary]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.centered}
      >
        <ActivityIndicator size="large" color={colors.primary} />
      </LinearGradient>
    );
  }

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LinearGradient
        colors={isDarkMode
          ? [colors.background, colors.backgroundSecondary, colors.backgroundTertiary]
          : [colors.background, colors.backgroundSecondary, colors.backgroundTertiary]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.container}
      >
      <View style={[styles.header, { borderBottomColor: `${colors.primary}20` }]}>
        <Text className="font-rubik-bold" style={[styles.headerTitle, { color: '#FFFFFF' }]}>
          Notifications
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          style={[styles.closeButton, {
            backgroundColor: `${colors.primary}10`,
            borderColor: `${colors.primary}30`
          }]}
        >
          <Ionicons name="close" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={() => {
              setRefreshing(true);
              fetchNotifications();
            }}
            tintColor={colors.primary}
            colors={[colors.primary]}
          />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text className="font-rubik-medium" style={[styles.emptyText, { color: colors.textSecondary }]}>
              No notifications yet
            </Text>
          </View>
        }
      />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: Platform.OS === "ios" ? 50 : 20,
    paddingBottom: 10,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 20,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  notificationItem: {
    flexDirection: "row",
    padding: 16,
    borderBottomWidth: 1,
    alignItems: "center",
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 1,
  },
  notificationContent: {
    flex: 1,
    marginLeft: 12,
  },
  username: {
    fontSize: 16,
    marginBottom: 4,
  },
  notificationText: {
    fontSize: 14,
  },
  timeAgo: {
    fontSize: 12,
    marginTop: 4,
  },
  unreadDot: {
    marginLeft: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
  },
  deleteButton: {
    padding: 8,
  },
});
